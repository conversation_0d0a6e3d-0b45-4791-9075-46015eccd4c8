services:
  prometheus:
    image: prom/prometheus
    container_name: prometheus
    restart: always
    ports:
      - "9090:9090"
    volumes:
      - ./prometheus:/etc/prometheus
      - prometheus-storage:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--web.enable-lifecycle'
    networks:
      - monitoring_network
      - db_network
    depends_on:
      - node-exporter
      - cadvisor

  grafana:
    image: grafana/grafana:latest
    container_name: grafana
    restart: always
    ports:
      - "3000:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=admin
    volumes:
      - /mnt/docker/grafana:/var/lib/grafana
    networks:
      - monitoring_network
      - db_network

  node-exporter:
    image: prom/node-exporter:latest
    container_name: node-exporter
    restart: always
    ports:
      - "9100:9100"
    command:
      - '--path.rootfs=/host'
    volumes:
      - '/:/host:ro,rslave'
    networks:
      - monitoring_network

  cadvisor:
    image: gcr.io/cadvisor/cadvisor:latest
    container_name: cadvisor
    restart: always
    ports:
      - "9011:8080"
    volumes:
      - /:/rootfs:ro
      - /var/run:/var/run:ro
      - /sys:/sys:ro
      - /var/lib/docker/:/var/lib/docker:ro
      - /dev/disk/:/dev/disk:ro
    privileged: true
    devices:
      - /dev/kmsg:/dev/kmsg
    networks:
      - monitoring_network

  # Optional: Redis exporter for Redis metrics
  redis-exporter:
    image: oliver006/redis_exporter
    container_name: redis-exporter
    restart: always
    ports:
      - "9121:9121"
    command:
      - '--redis.addr=redis://redis:6379'
    networks:
      - monitoring_network
      - db_network

  # Optional: PostgreSQL exporter for PostgreSQL metrics
  postgres-exporter:
    image: prometheuscommunity/postgres-exporter
    container_name: postgres-exporter
    restart: always
    ports:
      - "9187:9187"
    environment:
      DATA_SOURCE_NAME: "********************************************/dbname?sslmode=disable"
    networks:
      - monitoring_network
      - db_network

volumes:
  prometheus-storage:

networks:
  monitoring_network:
    driver: bridge
    name: monitoring_network
  
  db_network:
    external: true
    name: db_network


